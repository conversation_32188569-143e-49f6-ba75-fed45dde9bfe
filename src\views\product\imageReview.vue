<template>
  <div class="image-review-container">
    <!-- 图片审核区域 -->
    <div class="image-review-section">
      <div class="image-review-title" @click="showImageReview = !showImageReview">
        图片审核
        <i style="color: #3b95a8" :class="showImageReview ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
        <el-popover placement="top" width="300" trigger="hover">
          <div>此处为新品上报的图片审核流程，此处图片审核完，不会流转至原独立的图片待审核列表。请务必按照正常的图片审核要求进行审核。</div>
          <i class="info-suffix-icon el-icon-question" slot="reference"></i>
        </el-popover>
      </div>
      <div v-show="showImageReview" class="image-review-content">
        <!-- 图片审核内容区域 -->
        <div class="review-content">
          <el-form :model="reviewData" label-width="120px" size="small">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="审核状态">
                  <el-select v-model="reviewData.status" placeholder="请选择审核状态" @change="handleReviewStatusChange">
                    <el-option label="待审核" value="pending"></el-option>
                    <el-option label="审核通过" value="approved"></el-option>
                    <el-option label="审核拒绝" value="rejected"></el-option>
                    <el-option label="需要修改" value="revision"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="审核人">
                  <el-input v-model="reviewData.reviewer" placeholder="审核人姓名" readonly></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="审核时间">
                  <el-input v-model="reviewData.reviewTime" placeholder="审核时间" readonly></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="审核意见">
                  <el-input
                    v-model="reviewData.comments"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入审核意见..."
                    @change="handleReviewCommentsChange"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item>
                  <el-button type="primary" size="small" @click="saveReviewData">保存审核信息</el-button>
                  <el-button size="small" @click="resetReviewData">重置</el-button>
                  <el-button type="success" size="small" @click="submitReview" :disabled="!reviewData.status || reviewData.status === 'pending'">
                    提交审核
                  </el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
    </div>

    <!-- 主图展示区域 -->
    <div class="main-image-section">
      <div class="section-title">主图</div>
      <div class="main-image-content">
        <div class="main-image-upload">
          <el-upload
            :action="uploadUrl"
            list-type="picture-card"
            :disabled="disabled"
            :limit="1"
            :on-exceed="handleMainImageExceed"
            :before-upload="beforeUpload"
            :on-success="handleMainImageSuccess"
            :on-error="handleError"
            :file-list="mainImageList"
            :show-file-list="false"
          >
            <div v-if="!mainImage" class="upload-placeholder">
              <i class="el-icon-plus"></i>
              <div class="upload-text">上传主图</div>
            </div>
          </el-upload>

          <!-- 主图显示 -->
          <div v-if="mainImage" class="main-image-display">
            <div class="image-container">
              <img
                :src="mainImage.url || mainImage.mediaUrl"
                alt="主图"
                @click="previewMainImage"
                class="main-image"
              />
              <div class="image-actions">
                <span class="action-btn" @click="previewMainImage" title="预览">
                  <i class="el-icon-zoom-in"></i>
                </span>
                <span class="action-btn" @click="deleteMainImage" title="删除">
                  <i class="el-icon-delete"></i>
                </span>
              </div>
            </div>

            <!-- OCR功能区域 -->
            <div class="main-image-ocr">
              <div class="ocr-actions">
                <span
                  v-if="!isMainImageReady || mainImageOcrResult.loading"
                  class="ocr-extract-btn disabled"
                >
                  {{ mainImageOcrResult.loading ? '提取中' : '图片上传中' }}
                </span>
                <span
                  v-else-if="!mainImageOcrResult.extracted"
                  class="ocr-extract-btn"
                  @click="extractMainImageOCR"
                >
                  提取图片文字
                </span>
                <span
                  v-else
                  class="ocr-extract-btn extracted"
                  @click="showMainImageOCRResult"
                >
                  查看识别结果
                </span>
              </div>

              <!-- OCR结果显示 -->
              <div v-if="mainImageOcrResult.extracted && mainImageOcrResult.fields.length > 0" class="ocr-results">
                <div class="ocr-results-header">
                  <span>识别结果</span>
                  <span class="delete-ocr-btn" @click="deleteMainImageOCR" title="删除识别结果">
                    <i class="el-icon-delete"></i>
                  </span>
                </div>
                <div class="ocr-fields">
                  <div v-for="(field, index) in mainImageOcrResult.fields" :key="index" class="ocr-field">
                    <div class="field-label">{{ field.label }}</div>
                    <div class="field-value">{{ field.value }}</div>
                    <el-button
                      size="mini"
                      type="text"
                      @click="copyText(field.value)"
                      class="copy-btn"
                    >
                      复制
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 主图说明 -->
        <div class="main-image-note">
          <p><i class="el-icon-info"></i> 主图将自动从外包装图片的第一张图片获取</p>
        </div>
      </div>
    </div>

    <!-- 外包装图片区域 -->
    <div class="image-category-section">
      <div class="section-title">
        外包装图片
        <span class="drag-tip">（支持拖拽调整顺序）</span>
      </div>
      <div class="image-category-content">
        <div class="image-upload-drag-wrapper">
          <ImageUploadWithOCR
            ref="packagingUpload"
            :fileList="imageCategories.packaging.list"
            :uploadUrl="uploadUrl"
            :limit="imageCategories.packaging.limit"
            :disabled="disabled"
            :enableOCR="true"
            @change="handlePackagingChange"
          />

          <!-- 拖拽排序控制按钮 -->
          <div v-if="imageCategories.packaging.list.length > 1" class="drag-controls">
            <div class="drag-controls-title">图片排序</div>
            <draggable
              v-model="imageCategories.packaging.list"
              class="drag-list"
              :options="dragOptions"
              @end="onPackagingDragEnd"
            >
              <div
                v-for="(item, index) in imageCategories.packaging.list"
                :key="item.uid || item.mediaUrl || index"
                class="drag-item"
              >
                <img :src="item.url || item.mediaUrl" alt="" class="drag-thumb" />
                <span class="drag-index">{{ index + 1 }}</span>
                <div class="drag-actions">
                  <el-button size="mini" type="text" @click="movePackagingUp(index)" :disabled="index === 0">
                    <i class="el-icon-arrow-up"></i>
                  </el-button>
                  <el-button size="mini" type="text" @click="movePackagingDown(index)" :disabled="index === imageCategories.packaging.list.length - 1">
                    <i class="el-icon-arrow-down"></i>
                  </el-button>
                </div>
              </div>
            </draggable>
          </div>
        </div>
      </div>
    </div>

    <!-- 说明书图片区域 -->
    <div class="image-category-section">
      <div class="section-title">
        说明书图片
        <span class="drag-tip">（支持拖拽调整顺序）</span>
      </div>
      <div class="image-category-content">
        <div class="image-upload-drag-wrapper">
          <ImageUploadWithOCR
            ref="manualUpload"
            :fileList="imageCategories.manual.list"
            :uploadUrl="uploadUrl"
            :limit="imageCategories.manual.limit"
            :disabled="disabled"
            :enableOCR="true"
            @change="handleManualChange"
          />

          <!-- 拖拽排序控制按钮 -->
          <div v-if="imageCategories.manual.list.length > 1" class="drag-controls">
            <div class="drag-controls-title">图片排序</div>
            <draggable
              v-model="imageCategories.manual.list"
              class="drag-list"
              :options="dragOptions"
              @end="onManualDragEnd"
            >
              <div
                v-for="(item, index) in imageCategories.manual.list"
                :key="item.uid || item.mediaUrl || index"
                class="drag-item"
              >
                <img :src="item.url || item.mediaUrl" alt="" class="drag-thumb" />
                <span class="drag-index">{{ index + 1 }}</span>
                <div class="drag-actions">
                  <el-button size="mini" type="text" @click="moveManualUp(index)" :disabled="index === 0">
                    <i class="el-icon-arrow-up"></i>
                  </el-button>
                  <el-button size="mini" type="text" @click="moveManualDown(index)" :disabled="index === imageCategories.manual.list.length - 1">
                    <i class="el-icon-arrow-down"></i>
                  </el-button>
                </div>
              </div>
            </draggable>
          </div>
        </div>
      </div>
    </div>

    <!-- 单选按钮组控件 -->
    <div class="radio-controls-section">
      <div class="section-title">图片设置</div>
      <div class="radio-controls-content">
        <el-form :model="formData" label-width="140px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="是否使用渠道上传图片">
                <el-radio-group v-model="formData.useChannelUpload" @change="handleChannelUploadChange">
                  <el-radio :label="true">是</el-radio>
                  <el-radio :label="false">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="图片质量">
                <el-radio-group v-model="formData.imageQuality" @change="handleImageQualityChange">
                  <el-radio label="retouching">需设计精修</el-radio>
                  <el-radio label="standard">标准质量</el-radio>
                  <el-radio label="high">高质量</el-radio>
                  <el-radio label="other">其他</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <!-- 图片预览组件 -->
    <imagePreviewWithOCR
      :on-close="closeImageViewer"
      v-if="imgPreview"
      :url-list="imgPreviewList"
      :initialIndex="initialIndex"
      :enable-o-c-r="true"
      :ocr-results="{ [mainImage?.uid || mainImage?.mediaUrl || mainImage?.url]: mainImageOcrResult }"
      :extract-text-from-image="extractMainImageOCRInPreview"
      :copy-info="copyText"
    ></imagePreviewWithOCR>
  </div>
</template>

<script>
import imageConversion from "image-conversion";
import imagePreviewWithOCR from "@/components/common/preview/imagePreviewWithOCR";
import ImageUploadWithOCR from "@/views/product/components/ImageUploadWithOCR";
import draggable from "vuedraggable";
import request from "@/utils/request";
import { getOcrProxyPath } from "@/config/ocr";

export default {
  name: "ImageReview",
  components: {
    imagePreviewWithOCR,
    ImageUploadWithOCR,
    draggable,
  },
  props: {
    // 上传地址
    uploadUrl: {
      type: String,
      default: "",
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false,
    },
    // 最大文件大小(MB)
    maxSize: {
      type: Number,
      default: 10,
    },
    // 最小宽度
    minWidth: {
      type: Number,
      default: 0,
    },
    // 初始数据
    initialData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 图片审核区域
      showImageReview: true,

      // 主图数据
      mainImage: null,
      mainImageList: [],
      mainImageOcrResult: {
        loading: false,
        extracted: false,
        fields: []
      },

      // 三个图片类别数据
      imageCategories: {
        packaging: {        // 外包装图片
          title: '外包装图片',
          list: [],
          limit: 10
        },
        manual: {           // 说明书图片
          title: '说明书图片',
          list: [],
          limit: 10
        }
      },

      // 预览相关
      imgPreview: false,
      imgPreviewList: [],
      initialIndex: 0,

      // 拖拽配置
      dragOptions: {
        animation: 200,
        group: 'image-sort',
        disabled: false,
        ghostClass: 'ghost-item'
      },

      // 单选按钮组数据
      formData: {
        useChannelUpload: true,    // 是否使用渠道上传图片
        imageQuality: 'retouching' // 图片质量：retouching-需设计精修
      },

      // 图片审核数据
      reviewData: {
        status: 'pending',         // 审核状态：pending-待审核, approved-审核通过, rejected-审核拒绝, revision-需要修改
        reviewer: '',              // 审核人
        reviewTime: '',            // 审核时间
        comments: ''               // 审核意见
      },
    };
  },
  computed: {
    // 主图是否准备好进行OCR
    isMainImageReady() {
      if (!this.mainImage) return false;
      return this.mainImage.status === 'success' ||
             this.mainImage.url ||
             this.mainImage.mediaUrl ||
             (this.mainImage.response && this.mainImage.response.data);
    }
  },
  watch: {
    initialData: {
      handler(newVal) {
        if (newVal) {
          this.setImageData(newVal);
        }
      },
      immediate: true,
      deep: true
    },

    // 监听外包装图片变化，自动更新主图
    'imageCategories.packaging.list': {
      handler(newList) {
        if (newList && newList.length > 0) {
          this.syncMainImageFromPackaging();
        }
      },
      deep: true
    }
  },
  methods: {
    // 主图上传相关方法
    handleMainImageExceed() {
      this.$message.warning('主图只能上传一张图片');
    },

    beforeUpload(file) {
      // 检查文件类型
      const isImage = /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(file.name);
      if (!isImage) {
        this.$message.error('只能上传图片文件');
        return false;
      }

      // 检查文件大小
      const isMaxSize = file.size / 1024 / 1024 < this.maxSize;
      if (!isMaxSize) {
        this.$message.error(`图片大小不能超过 ${this.maxSize}MB`);
        return false;
      }

      // 检查图片宽度
      if (this.minWidth > 0) {
        return this.checkWidth(file);
      }

      // 对大于1M的图片进行压缩
      if (file.size / 1024 / 1024 > 1) {
        return imageConversion.compress(file, 0.6);
      }

      return true;
    },

    checkWidth(file) {
      return new Promise((resolve, reject) => {
        let _URL = window.URL || window.webkitURL;
        let img = new Image();
        img.onload = () => {
          let valid = img.width >= this.minWidth;
          if (valid) {
            resolve();
          } else {
            this.$message.error(`图片宽度不能小于 ${this.minWidth}px`);
            reject();
          }
        };
        img.src = _URL.createObjectURL(file);
      }).then(
        () => true,
        () => false
      );
    },

    handleMainImageSuccess(response, file) {
      this.mainImage = {
        ...file,
        url: response.data?.mediaUrl || file.url,
        mediaUrl: response.data?.mediaUrl || file.url
      };
      this.mainImageList = [this.mainImage];
      this.resetMainImageOCR();
      this.$emit('main-image-change', this.mainImage);
    },

    handleError(error) {
      this.$message.error('图片上传失败');
      console.error('Upload error:', error);
    },

    deleteMainImage() {
      this.$confirm('确定要删除主图吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.mainImage = null;
        this.mainImageList = [];
        this.resetMainImageOCR();
        this.$emit('main-image-change', null);
        this.$message.success('主图已删除');
      }).catch(() => {});
    },

    previewMainImage() {
      if (!this.mainImage) return;

      this.imgPreviewList = [{
        uid: this.mainImage.uid,
        name: this.mainImage.name || 'main-image',
        mediaName: this.mainImage.name || 'main-image',
        mediaUrl: this.mainImage.url || this.mainImage.mediaUrl,
        url: this.mainImage.url || this.mainImage.mediaUrl,
        status: this.mainImage.status
      }];
      this.initialIndex = 0;
      this.imgPreview = true;
    },

    closeImageViewer() {
      this.imgPreview = false;
    },

    // OCR相关方法
    resetMainImageOCR() {
      this.mainImageOcrResult = {
        loading: false,
        extracted: false,
        fields: []
      };
    },

    async extractMainImageOCR() {
      if (!this.mainImage || this.mainImageOcrResult.extracted) {
        if (this.mainImageOcrResult.extracted) {
          this.$message.warning('已成功提取图片文字，勿重复操作');
        }
        return;
      }

      this.mainImageOcrResult.loading = true;

      try {
        const imgUrl = this.getImageUrl(this.mainImage);
        if (!imgUrl) {
          throw new Error('图片URL无效');
        }

        const ocrPath = getOcrProxyPath();
        const response = await request({
          url: ocrPath,
          method: 'POST',
          data: {
            imgUrl: imgUrl,
            type: 'GENERAL_STRUCTURE'
          }
        });

        const responseData = response.data || response;
        if (responseData.code !== 0) {
          throw new Error(responseData.msg || 'OCR识别失败');
        }

        // 处理OCR结果
        const ocrData = responseData.result?.Data || responseData.Data || [];
        const fields = this.processOcrData(ocrData);

        this.mainImageOcrResult = {
          loading: false,
          extracted: true,
          fields: fields
        };

        this.$message.success('图片文字提取成功');

      } catch (error) {
        console.error('OCR提取失败:', error);
        this.mainImageOcrResult.loading = false;
        this.$message.error(error.message || 'OCR识别失败，请重试');
      }
    },

    getImageUrl(file) {
      if (file.url) return file.url;
      if (file.mediaUrl) return file.mediaUrl;
      if (file.response && file.response.data && file.response.data.mediaUrl) {
        return file.response.data.mediaUrl;
      }
      return null;
    },

    processOcrData(ocrData) {
      if (!Array.isArray(ocrData)) return [];

      return ocrData.map((item, index) => ({
        id: `field_${index}`,
        label: item.key || item.label || `字段${index + 1}`,
        value: item.value || item.text || ''
      })).filter(field => field.value.trim());
    },

    showMainImageOCRResult() {
      // 显示OCR结果，可以滚动到结果区域
      const ocrResultsEl = this.$el.querySelector('.ocr-results');
      if (ocrResultsEl) {
        ocrResultsEl.scrollIntoView({ behavior: 'smooth' });
      }
    },

    deleteMainImageOCR() {
      this.$confirm('确定要删除主图的识别结果吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.resetMainImageOCR();
        this.$message.success('识别结果已删除');
      }).catch(() => {});
    },

    copyText(text) {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
          this.$message.success('复制成功');
        }).catch(() => {
          this.fallbackCopyText(text);
        });
      } else {
        this.fallbackCopyText(text);
      }
    },

    fallbackCopyText(text) {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        this.$message.success('复制成功');
      } catch (err) {
        this.$message.error('复制失败');
      }
      document.body.removeChild(textArea);
    },

    // 在预览组件中提取OCR文字的方法
    async extractMainImageOCRInPreview() {
      // 检查是否已经提取过
      if (this.mainImageOcrResult.extracted) {
        this.$message.info('已成功提取图片文字，勿重复操作');
        return;
      }

      // 调用主要的OCR提取方法
      await this.extractMainImageOCR();
    },

    // 图片类别管理方法
    handlePackagingChange(fileList) {
      this.imageCategories.packaging.list = fileList;

      // 如果外包装图片有变化，自动更新主图
      this.syncMainImageFromPackaging();

      this.$emit('packaging-change', fileList);
    },

    handleManualChange(fileList) {
      this.imageCategories.manual.list = fileList;
      this.$emit('manual-change', fileList);
    },

    // 从外包装图片同步主图
    syncMainImageFromPackaging() {
      const packagingList = this.imageCategories.packaging.list;
      if (packagingList && packagingList.length > 0) {
        const firstImage = packagingList[0];
        if (!this.mainImage || this.mainImage.uid !== firstImage.uid) {
          this.mainImage = { ...firstImage };
          this.mainImageList = [this.mainImage];
          this.resetMainImageOCR();
          this.$emit('main-image-change', this.mainImage);
        }
      } else if (this.mainImage) {
        // 如果外包装图片为空，清空主图
        this.mainImage = null;
        this.mainImageList = [];
        this.resetMainImageOCR();
        this.$emit('main-image-change', null);
      }
    },

    // 获取所有图片数据
    getAllImageData() {
      return {
        mainImage: this.mainImage,
        packaging: this.imageCategories.packaging.list,
        manual: this.imageCategories.manual.list
      };
    },

    // 设置初始数据
    setImageData(data) {
      if (data.packaging) {
        this.imageCategories.packaging.list = data.packaging;
      }
      if (data.manual) {
        this.imageCategories.manual.list = data.manual;
      }
      if (data.mainImage) {
        this.mainImage = data.mainImage;
        this.mainImageList = [data.mainImage];
      }

      // 同步主图
      this.syncMainImageFromPackaging();
    },

    // 拖拽排序相关方法
    onPackagingDragEnd() {
      // 拖拽结束后更新组件
      this.$nextTick(() => {
        this.$refs.packagingUpload.list = [...this.imageCategories.packaging.list];
        this.syncMainImageFromPackaging();
        this.$emit('packaging-change', this.imageCategories.packaging.list);
      });
    },

    onManualDragEnd() {
      // 拖拽结束后更新组件
      this.$nextTick(() => {
        this.$refs.manualUpload.list = [...this.imageCategories.manual.list];
        this.$emit('manual-change', this.imageCategories.manual.list);
      });
    },

    // 外包装图片位置调整
    movePackagingUp(index) {
      if (index > 0) {
        const item = this.imageCategories.packaging.list.splice(index, 1)[0];
        this.imageCategories.packaging.list.splice(index - 1, 0, item);
        this.onPackagingDragEnd();
      }
    },

    movePackagingDown(index) {
      if (index < this.imageCategories.packaging.list.length - 1) {
        const item = this.imageCategories.packaging.list.splice(index, 1)[0];
        this.imageCategories.packaging.list.splice(index + 1, 0, item);
        this.onPackagingDragEnd();
      }
    },

    // 说明书图片位置调整
    moveManualUp(index) {
      if (index > 0) {
        const item = this.imageCategories.manual.list.splice(index, 1)[0];
        this.imageCategories.manual.list.splice(index - 1, 0, item);
        this.onManualDragEnd();
      }
    },

    moveManualDown(index) {
      if (index < this.imageCategories.manual.list.length - 1) {
        const item = this.imageCategories.manual.list.splice(index, 1)[0];
        this.imageCategories.manual.list.splice(index + 1, 0, item);
        this.onManualDragEnd();
      }
    },

    // 单选按钮组处理方法
    handleChannelUploadChange(value) {
      this.$emit('channel-upload-change', value);
      console.log('是否使用渠道上传图片:', value ? '是' : '否');
    },

    handleImageQualityChange(value) {
      this.$emit('image-quality-change', value);
      console.log('图片质量:', value);
    },

    // 获取表单数据
    getFormData() {
      return {
        ...this.formData
      };
    },

    // 设置表单数据
    setFormData(data) {
      if (data.useChannelUpload !== undefined) {
        this.formData.useChannelUpload = data.useChannelUpload;
      }
      if (data.imageQuality !== undefined) {
        this.formData.imageQuality = data.imageQuality;
      }
    },

    // 验证表单
    validateForm() {
      // 这里可以添加表单验证逻辑
      return true;
    },

    // 图片审核相关方法
    handleReviewStatusChange(value) {
      if (value !== 'pending') {
        // 自动填充审核人和审核时间
        this.reviewData.reviewer = this.$store.getters.name || '当前用户';
        this.reviewData.reviewTime = new Date().toLocaleString();
      } else {
        this.reviewData.reviewer = '';
        this.reviewData.reviewTime = '';
      }
      this.$emit('review-status-change', value);
    },

    handleReviewCommentsChange(value) {
      this.$emit('review-comments-change', value);
    },

    saveReviewData() {
      const reviewInfo = {
        ...this.reviewData,
        timestamp: Date.now()
      };
      this.$emit('save-review', reviewInfo);
      this.$message.success('审核信息已保存');
    },

    resetReviewData() {
      this.reviewData = {
        status: 'pending',
        reviewer: '',
        reviewTime: '',
        comments: ''
      };
      this.$message.info('审核信息已重置');
    },

    submitReview() {
      if (!this.reviewData.status || this.reviewData.status === 'pending') {
        this.$message.warning('请先选择审核状态');
        return;
      }

      if (!this.reviewData.comments.trim()) {
        this.$message.warning('请填写审核意见');
        return;
      }

      this.$confirm('确定要提交审核结果吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const reviewResult = {
          ...this.reviewData,
          submitTime: new Date().toLocaleString(),
          allImageData: this.getAllImageData()
        };
        this.$emit('submit-review', reviewResult);
        this.$message.success('审核结果已提交');
      }).catch(() => {});
    },

    // 获取审核数据
    getReviewData() {
      return {
        ...this.reviewData
      };
    },

    // 设置审核数据
    setReviewData(data) {
      if (data.status !== undefined) {
        this.reviewData.status = data.status;
      }
      if (data.reviewer !== undefined) {
        this.reviewData.reviewer = data.reviewer;
      }
      if (data.reviewTime !== undefined) {
        this.reviewData.reviewTime = data.reviewTime;
      }
      if (data.comments !== undefined) {
        this.reviewData.comments = data.comments;
      }
    },

    // 公共方法：获取所有数据
    getAllData() {
      return {
        imageData: this.getAllImageData(),
        formData: this.getFormData(),
        reviewData: this.getReviewData()
      };
    },

    // 公共方法：设置所有数据
    setAllData(data) {
      if (data.imageData) {
        this.setImageData(data.imageData);
      }
      if (data.formData) {
        this.setFormData(data.formData);
      }
      if (data.reviewData) {
        this.setReviewData(data.reviewData);
      }
    },

    // 公共方法：重置所有数据
    resetAllData() {
      // 重置图片数据
      this.mainImage = null;
      this.mainImageList = [];
      this.resetMainImageOCR();
      this.imageCategories.packaging.list = [];
      this.imageCategories.manual.list = [];

      // 重置表单数据
      this.formData = {
        useChannelUpload: true,
        imageQuality: 'retouching'
      };

      // 重置审核数据
      this.resetReviewData();

      this.$message.info('所有数据已重置');
    },

    // 公共方法：验证所有数据
    validateAllData() {
      const errors = [];

      // 验证图片数据
      if (!this.mainImage && this.imageCategories.packaging.list.length === 0) {
        errors.push('请至少上传一张图片');
      }

      // 验证表单数据
      if (!this.validateForm()) {
        errors.push('表单数据验证失败');
      }

      // 验证审核数据（如果需要）
      if (this.reviewData.status === 'rejected' && !this.reviewData.comments.trim()) {
        errors.push('审核拒绝时必须填写审核意见');
      }

      return {
        valid: errors.length === 0,
        errors: errors
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.image-review-container {
  width: 100%;

  // 图片审核区域
  .image-review-section {
    margin-bottom: 24px;

    .image-review-title {
      font-weight: 600;
      margin-bottom: 12px;
      border-bottom: 1px solid #e4e4eb;
      line-height: 50px;
      padding-left: 20px;
      position: relative;
      cursor: pointer;

      &:hover {
        background-color: #f5f7fa;
      }

      .info-suffix-icon {
        margin: 12px 0 0 5px;
        color: #77787e;
        cursor: pointer;
      }
    }

    .image-review-content {
      padding: 16px 20px;
      background-color: #f9f9f9;
      border-radius: 4px;

      .review-content {
        /deep/ .el-form {
          .el-form-item {
            margin-bottom: 16px;

            .el-form-item__label {
              font-weight: 500;
              color: #606266;
            }

            .el-select,
            .el-input {
              width: 100%;
            }

            .el-textarea {
              .el-textarea__inner {
                resize: vertical;
                min-height: 80px;
              }
            }
          }

          .el-button {
            margin-right: 10px;

            &:last-child {
              margin-right: 0;
            }
          }
        }
      }
    }
  }

  // 主图展示区域
  .main-image-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 16px;
      color: #303133;
    }

    .main-image-content {
      .main-image-upload {
        display: flex;
        gap: 20px;

        /deep/ .el-upload {
          border: 1px dashed #d9d9d9;
          border-radius: 6px;
          cursor: pointer;
          position: relative;
          overflow: hidden;
          width: 148px;
          height: 148px;

          &:hover {
            border-color: #409eff;
          }

          .upload-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;

            .el-icon-plus {
              font-size: 28px;
              color: #8c939d;
              margin-bottom: 8px;
            }

            .upload-text {
              color: #8c939d;
              font-size: 14px;
            }
          }
        }

        .main-image-display {
          display: flex;
          gap: 20px;

          .image-container {
            position: relative;
            width: 148px;
            height: 148px;
            border: 1px solid #e4e4eb;
            border-radius: 6px;
            overflow: hidden;

            .main-image {
              width: 100%;
              height: 100%;
              object-fit: cover;
              cursor: pointer;
            }

            .image-actions {
              position: absolute;
              top: 0;
              right: 0;
              background: rgba(0, 0, 0, 0.5);
              display: flex;
              opacity: 0;
              transition: opacity 0.3s;

              .action-btn {
                color: white;
                padding: 8px;
                cursor: pointer;

                &:hover {
                  background: rgba(0, 0, 0, 0.7);
                }
              }
            }

            &:hover .image-actions {
              opacity: 1;
            }
          }

          .main-image-ocr {
            flex: 1;
            min-width: 300px;

            @media (max-width: 768px) {
              min-width: auto;
            }

            .ocr-actions {
              margin-bottom: 12px;

              .ocr-extract-btn {
                display: inline-block;
                padding: 8px 16px;
                background: #409eff;
                color: white;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;

                &:hover {
                  background: #66b1ff;
                }

                &.disabled {
                  background: #c0c4cc;
                  cursor: not-allowed;
                }

                &.extracted {
                  background: #67c23a;

                  &:hover {
                    background: #85ce61;
                  }
                }
              }
            }

            .ocr-results {
              border: 1px solid #e4e4eb;
              border-radius: 4px;
              background: white;

              .ocr-results-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 12px 16px;
                background: #f5f7fa;
                border-bottom: 1px solid #e4e4eb;
                font-weight: 600;

                .delete-ocr-btn {
                  color: #f56c6c;
                  cursor: pointer;

                  &:hover {
                    color: #f78989;
                  }
                }
              }

              .ocr-fields {
                max-height: 200px;
                overflow-y: auto;

                .ocr-field {
                  display: flex;
                  align-items: center;
                  padding: 8px 16px;
                  border-bottom: 1px solid #f0f0f0;

                  &:last-child {
                    border-bottom: none;
                  }

                  .field-label {
                    width: 80px;
                    font-weight: 500;
                    color: #606266;
                    flex-shrink: 0;
                  }

                  .field-value {
                    flex: 1;
                    margin: 0 12px;
                    word-break: break-all;
                  }

                  .copy-btn {
                    flex-shrink: 0;
                  }
                }
              }
            }
          }
        }
      }

      .main-image-note {
        margin-top: 12px;
        padding: 8px 12px;
        background: #f0f9ff;
        border: 1px solid #b3d8ff;
        border-radius: 4px;
        color: #409eff;
        font-size: 14px;

        .el-icon-info {
          margin-right: 4px;
        }
      }
    }
  }

  // 图片类别区域
  .image-category-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 16px;
      color: #303133;

      .drag-tip {
        font-size: 12px;
        color: #909399;
        font-weight: normal;
        margin-left: 8px;
      }
    }

    .image-category-content {
      width: 100%;

      .image-upload-drag-wrapper {
        display: flex;
        gap: 20px;

        // ImageUploadWithOCR 组件区域
        > div:first-child {
          flex: 1;
        }

        // 拖拽控制区域
        .drag-controls {
          width: 200px;
          flex-shrink: 0;

          .drag-controls-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #606266;
          }

          .drag-list {
            border: 1px solid #e4e4eb;
            border-radius: 4px;
            background: white;
            max-height: 300px;
            overflow-y: auto;

            .drag-item {
              display: flex;
              align-items: center;
              padding: 8px 12px;
              border-bottom: 1px solid #f0f0f0;
              cursor: move;
              transition: background-color 0.3s;

              &:last-child {
                border-bottom: none;
              }

              &:hover {
                background-color: #f5f7fa;
              }

              .drag-thumb {
                width: 40px;
                height: 40px;
                object-fit: cover;
                border-radius: 4px;
                border: 1px solid #e4e4eb;
                margin-right: 8px;
              }

              .drag-index {
                width: 20px;
                text-align: center;
                font-weight: 600;
                color: #409eff;
                margin-right: 8px;
              }

              .drag-actions {
                margin-left: auto;
                display: flex;
                flex-direction: column;
                gap: 2px;

                .el-button {
                  padding: 2px 4px;
                  min-height: auto;

                  &:disabled {
                    color: #c0c4cc;
                  }
                }
              }
            }

            // 拖拽时的幽灵样式
            .ghost-item {
              opacity: 0.5;
              background-color: #f0f9ff;
            }
          }
        }
      }
    }
  }

  // 单选按钮组控件区域
  .radio-controls-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 16px;
      color: #303133;
    }

    .radio-controls-content {
      background: #f9f9f9;
      border: 1px solid #e4e4eb;
      border-radius: 6px;
      padding: 20px;

      /deep/ .el-form {
        margin-bottom: 0;

        .el-form-item {
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          .el-form-item__label {
            font-weight: 500;
            color: #606266;
          }

          .el-radio-group {
            .el-radio {
              margin-right: 20px;

              &:last-child {
                margin-right: 0;
              }

              .el-radio__label {
                font-size: 14px;
              }
            }
          }
        }
      }
    }
  }
}

// 响应式布局
@media (max-width: 1200px) {
  .image-review-container {
    .main-image-section {
      .main-image-content {
        .main-image-upload {
          .main-image-display {
            flex-direction: column;
            gap: 16px;

            .main-image-ocr {
              min-width: auto;
            }
          }
        }
      }
    }

    .image-category-section {
      .image-category-content {
        .image-upload-drag-wrapper {
          flex-direction: column;

          .drag-controls {
            width: 100%;

            .drag-list {
              .drag-item {
                .drag-thumb {
                  width: 60px;
                  height: 60px;
                }
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .image-review-container {
    padding: 0 10px;

    .image-review-section,
    .main-image-section,
    .image-category-section,
    .radio-controls-section {
      margin-bottom: 16px;
    }

    .section-title {
      font-size: 14px !important;
      margin-bottom: 12px !important;

      .drag-tip {
        display: block;
        margin-left: 0 !important;
        margin-top: 4px;
      }
    }

    .main-image-section {
      .main-image-content {
        .main-image-upload {
          flex-direction: column;
          gap: 12px;

          /deep/ .el-upload {
            width: 120px;
            height: 120px;
          }

          .main-image-display {
            .image-container {
              width: 120px;
              height: 120px;
            }

            .main-image-ocr {
              .ocr-results {
                .ocr-fields {
                  .ocr-field {
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 8px;

                    .field-label {
                      width: auto;
                    }

                    .field-value {
                      margin: 0;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    .image-category-section {
      .image-category-content {
        .image-upload-drag-wrapper {
          .drag-controls {
            .drag-list {
              .drag-item {
                padding: 6px 8px;

                .drag-thumb {
                  width: 50px;
                  height: 50px;
                }

                .drag-actions {
                  flex-direction: row;
                  gap: 4px;
                }
              }
            }
          }
        }
      }
    }

    .radio-controls-section {
      .radio-controls-content {
        padding: 12px;

        /deep/ .el-form {
          .el-row {
            .el-col {
              margin-bottom: 12px;

              &:last-child {
                margin-bottom: 0;
              }
            }
          }

          .el-form-item {
            margin-bottom: 12px;

            .el-form-item__label {
              line-height: 1.4;
              padding-bottom: 4px;
            }

            .el-radio-group {
              .el-radio {
                display: block;
                margin-right: 0;
                margin-bottom: 8px;

                &:last-child {
                  margin-bottom: 0;
                }
              }
            }
          }
        }
      }
    }

    .image-review-content {
      padding: 12px;

      .review-content {
        /deep/ .el-form {
          .el-row {
            .el-col {
              margin-bottom: 12px;

              &:last-child {
                margin-bottom: 0;
              }
            }
          }

          .el-form-item {
            margin-bottom: 12px;

            .el-form-item__label {
              line-height: 1.4;
              padding-bottom: 4px;
            }
          }

          .el-button {
            width: 100%;
            margin-right: 0;
            margin-bottom: 8px;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .image-review-container {
    .main-image-section {
      .main-image-content {
        .main-image-upload {
          /deep/ .el-upload {
            width: 100px;
            height: 100px;
          }

          .main-image-display {
            .image-container {
              width: 100px;
              height: 100px;
            }
          }
        }
      }
    }

    .image-category-section {
      .image-category-content {
        .image-upload-drag-wrapper {
          .drag-controls {
            .drag-list {
              .drag-item {
                .drag-thumb {
                  width: 40px;
                  height: 40px;
                }

                .drag-index {
                  width: 16px;
                  font-size: 12px;
                }
              }
            }
          }
        }
      }
    }

    .radio-controls-section {
      .radio-controls-content {
        /deep/ .el-form {
          .el-form-item__label {
            width: 100% !important;
            text-align: left !important;
            margin-bottom: 8px;
          }

          .el-form-item__content {
            margin-left: 0 !important;
          }
        }
      }
    }

    .image-review-content {
      .review-content {
        /deep/ .el-form {
          .el-form-item__label {
            width: 100% !important;
            text-align: left !important;
            margin-bottom: 8px;
          }

          .el-form-item__content {
            margin-left: 0 !important;
          }
        }
      }
    }
  }
}
</style>