<template>
    <div class="image-review-container">
        <div class="image-review-title" @click="showForm = !showForm">
            图片审核
            <i style="color: #3b95a8" :class="showForm ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
            <el-popover placement="top" width="200" trigger="hover"
                :content="$store.getters.info[1] ? $store.getters.info[1].showContent : ''">
                <i class="info-suffix-icon el-icon-question" slot="reference"></i>
            </el-popover>
        </div>
    </div>
</template>

<script>
export default {

}
</script>

<style lang="scss" scoped>
.image-review-container {
    width: 100%;

    .image-review-title {
        font-weight: 600;
        margin-bottom: 22px;
        border-bottom: 1px solid #e4e4eb;
        line-height: 50px;
        padding-left: 20px;
        position: relative;

        .tip {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            right: 15px;
            color: #3b95a8;
        }
    }

    .info-suffix-icon {
        margin: 12px 0 0 5px;
        color: #77787e;
        cursor: pointer;
    }
}
</style>