Stack trace:
Frame         Function      Args
0007FFFF9DD0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8CD0) msys-2.0.dll+0x1FE8E
0007FFFF9DD0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA0A8) msys-2.0.dll+0x67F9
0007FFFF9DD0  000210046832 (000210286019, 0007FFFF9C88, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9DD0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9DD0  000210068E24 (0007FFFF9DE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA0B0  00021006A225 (0007FFFF9DE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFACD040000 ntdll.dll
7FFACC2D0000 KERNEL32.DLL
7FFACA460000 KERNELBASE.dll
7FFACB7A0000 USER32.dll
7FFACABD0000 win32u.dll
7FFACC2A0000 GDI32.dll
7FFACAA90000 gdi32full.dll
7FFACA310000 msvcp_win.dll
7FFACAC00000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFACC3A0000 advapi32.dll
7FFACCEC0000 msvcrt.dll
7FFACC810000 sechost.dll
7FFACB380000 RPCRT4.dll
7FFAC96A0000 CRYPTBASE.DLL
7FFACA3C0000 bcryptPrimitives.dll
7FFACB760000 IMM32.DLL
